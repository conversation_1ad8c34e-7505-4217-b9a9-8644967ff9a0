import { Client } from "@replit/object-storage";
import { nanoid } from "nanoid";
import fs from "fs";
import path from "path";

// Interface for storage service to allow local/remote implementations
interface IObjectStorage {
  uploadFile(buffer: Buffer, analysisId: number, fileExtension: string): Promise<string>;
  uploadPDF(buffer: Buffer, analysisId: number): Promise<string>;
  downloadFile(filename: string): Promise<Buffer>;
}

class LocalStorageService implements IObjectStorage {
  private uploadsDir: string;
  private reportsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
    this.reportsDir = path.join(process.cwd(), 'reports');

    // Ensure directories exist
    if (!fs.existsSync(this.uploadsDir)) {
      fs.mkdirSync(this.uploadsDir, { recursive: true });
    }
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * Upload a file buffer to local storage with a unique filename
   */
  async uploadFile(buffer: Buffer, analysisId: number, fileExtension: string): Promise<string> {
    const uniqueFilename = `uploads/${analysisId}-${nanoid()}.${fileExtension}`;
    const filePath = path.join(process.cwd(), uniqueFilename);

    await fs.promises.writeFile(filePath, buffer);

    return uniqueFilename;
  }

  /**
   * Upload a PDF buffer to local storage with a unique filename
   */
  async uploadPDF(buffer: Buffer, analysisId: number): Promise<string> {
    const uniqueFilename = `reports/report-${analysisId}-${nanoid()}.pdf`;
    const filePath = path.join(process.cwd(), uniqueFilename);

    await fs.promises.writeFile(filePath, buffer);

    return uniqueFilename;
  }

  /**
   * Download a file from local storage
   */
  async downloadFile(filename: string): Promise<Buffer> {
    const filePath = path.join(process.cwd(), filename);

    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filename}`);
    }

    return await fs.promises.readFile(filePath);
  }

  /**
   * Check if a file exists in local storage
   */
  async fileExists(filename: string): Promise<boolean> {
    const filePath = path.join(process.cwd(), filename);
    return fs.existsSync(filePath);
  }

  /**
   * Delete a file from local storage
   */
  async deleteFile(filename: string): Promise<boolean> {
    try {
      const filePath = path.join(process.cwd(), filename);
      if (fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  /**
   * List files with a prefix (for cleanup operations)
   */
  async listFiles(prefix?: string): Promise<string[]> {
    try {
      const files: string[] = [];

      // Check uploads directory
      if (fs.existsSync(this.uploadsDir)) {
        const uploadFiles = await fs.promises.readdir(this.uploadsDir);
        files.push(...uploadFiles.map(f => `uploads/${f}`));
      }

      // Check reports directory
      if (fs.existsSync(this.reportsDir)) {
        const reportFiles = await fs.promises.readdir(this.reportsDir);
        files.push(...reportFiles.map(f => `reports/${f}`));
      }

      // Filter by prefix if provided
      if (prefix) {
        return files.filter(f => f.startsWith(prefix));
      }

      return files;
    } catch (error) {
      console.error('Error listing files:', error);
      return [];
    }
  }
}

class ObjectStorageService implements IObjectStorage {
  private client: Client;

  constructor() {
    this.client = new Client();
  }

  /**
   * Upload a file buffer to object storage with a unique filename
   */
  async uploadFile(buffer: Buffer, analysisId: number, fileExtension: string): Promise<string> {
    const uniqueFilename = `uploads/${analysisId}-${nanoid()}.${fileExtension}`;
    
    const { ok, error } = await this.client.uploadFromBytes(uniqueFilename, buffer);
    
    if (!ok) {
      throw new Error(`Failed to upload file: ${error?.message || 'Unknown error'}`);
    }
    
    return uniqueFilename;
  }

  /**
   * Upload a PDF buffer to object storage with a unique filename
   */
  async uploadPDF(buffer: Buffer, analysisId: number): Promise<string> {
    const uniqueFilename = `reports/report-${analysisId}-${nanoid()}.pdf`;
    
    const { ok, error } = await this.client.uploadFromBytes(uniqueFilename, buffer);
    
    if (!ok) {
      throw new Error(`Failed to upload PDF: ${error?.message || 'Unknown error'}`);
    }
    
    return uniqueFilename;
  }

  /**
   * Download a file from object storage
   */
  async downloadFile(filename: string): Promise<Buffer> {
    const { ok, value, error } = await this.client.downloadAsBytes(filename);
    
    if (!ok || !value) {
      throw new Error(`Failed to download file: ${error?.message || 'File not found'}`);
    }
    
    return value;
  }

  /**
   * Check if a file exists in object storage
   */
  async fileExists(filename: string): Promise<boolean> {
    const { ok, value } = await this.client.exists(filename);
    return ok && value;
  }

  /**
   * Delete a file from object storage
   */
  async deleteFile(filename: string): Promise<boolean> {
    const { ok } = await this.client.delete(filename);
    return ok;
  }

  /**
   * List files with a prefix (for cleanup operations)
   */
  async listFiles(prefix?: string): Promise<string[]> {
    const { ok, value } = await this.client.list({ prefix });
    
    if (!ok || !value) {
      return [];
    }
    
    return value.map(obj => obj.name);
  }
}

// Create storage instance based on environment
let objectStorage: IObjectStorage;

if (process.env.NODE_ENV === 'local') {
  console.log('📁 Using local file storage');
  objectStorage = new LocalStorageService();
} else {
  console.log('☁️  Using Replit Object Storage');
  objectStorage = new ObjectStorageService();
}

export { objectStorage };
