import { Client } from "@replit/object-storage";
import { nanoid } from "nanoid";

class ObjectStorageService {
  private client: Client;

  constructor() {
    this.client = new Client();
  }

  /**
   * Upload a file buffer to object storage with a unique filename
   */
  async uploadFile(buffer: Buffer, analysisId: number, fileExtension: string): Promise<string> {
    const uniqueFilename = `uploads/${analysisId}-${nanoid()}.${fileExtension}`;
    
    const { ok, error } = await this.client.uploadFromBytes(uniqueFilename, buffer);
    
    if (!ok) {
      throw new Error(`Failed to upload file: ${error?.message || 'Unknown error'}`);
    }
    
    return uniqueFilename;
  }

  /**
   * Upload a PDF buffer to object storage with a unique filename
   */
  async uploadPDF(buffer: Buffer, analysisId: number): Promise<string> {
    const uniqueFilename = `reports/report-${analysisId}-${nanoid()}.pdf`;
    
    const { ok, error } = await this.client.uploadFromBytes(uniqueFilename, buffer);
    
    if (!ok) {
      throw new Error(`Failed to upload PDF: ${error?.message || 'Unknown error'}`);
    }
    
    return uniqueFilename;
  }

  /**
   * Download a file from object storage
   */
  async downloadFile(filename: string): Promise<Buffer> {
    const { ok, value, error } = await this.client.downloadAsBytes(filename);
    
    if (!ok || !value) {
      throw new Error(`Failed to download file: ${error?.message || 'File not found'}`);
    }
    
    return value;
  }

  /**
   * Check if a file exists in object storage
   */
  async fileExists(filename: string): Promise<boolean> {
    const { ok, value } = await this.client.exists(filename);
    return ok && value;
  }

  /**
   * Delete a file from object storage
   */
  async deleteFile(filename: string): Promise<boolean> {
    const { ok } = await this.client.delete(filename);
    return ok;
  }

  /**
   * List files with a prefix (for cleanup operations)
   */
  async listFiles(prefix?: string): Promise<string[]> {
    const { ok, value } = await this.client.list({ prefix });
    
    if (!ok || !value) {
      return [];
    }
    
    return value.map(obj => obj.name);
  }
}

export const objectStorage = new ObjectStorageService();
