import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { eq } from "drizzle-orm";
import { analyses, type Analysis, type InsertAnalysis } from "@shared/schema";
import { IStorage } from "./storage";

export class DatabaseStorage implements IStorage {
  private db: ReturnType<typeof drizzle>;
  private client: ReturnType<typeof postgres>;

  constructor() {
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable is required");
    }
    
    this.client = postgres(process.env.DATABASE_URL);
    this.db = drizzle(this.client);
  }

  async createAnalysis(analysis: InsertAnalysis): Promise<number> {
    const result = await this.db
      .insert(analyses)
      .values({
        ...analysis,
        progress: {
          stage: "idle",
          currentStep: "",
          percentage: 0,
          steps: [
            { name: "Extracting Files", status: "pending" },
            { name: "Parsing Files", status: "pending" },
            { name: "Analyzing Code", status: "pending" },
            { name: "Generating Report", status: "pending" }
          ]
        },
        paymentStatus: 'pending'
      })
      .returning({ id: analyses.id });
    
    return result[0].id;
  }

  async getAnalysis(id: number): Promise<Analysis | undefined> {
    const result = await this.db
      .select()
      .from(analyses)
      .where(eq(analyses.id, id))
      .limit(1);
    
    return result[0];
  }

  async updateAnalysisStatus(
    id: number, 
    status: 'processing' | 'completed' | 'failed', 
    progress?: any
  ): Promise<void> {
    const updateData: any = { 
      status,
      updatedAt: new Date()
    };
    
    if (progress) {
      updateData.progress = progress;
    }
    
    await this.db
      .update(analyses)
      .set(updateData)
      .where(eq(analyses.id, id));
  }

  async updateAnalysisResults(id: number, results: any): Promise<void> {
    await this.db
      .update(analyses)
      .set({
        projectInfo: results.projectInfo,
        scoreData: results.scoreData,
        updatedAt: new Date()
      })
      .where(eq(analyses.id, id));
  }

  async listAnalyses(): Promise<Analysis[]> {
    return await this.db
      .select()
      .from(analyses)
      .orderBy(analyses.createdAt);
  }

  // Premium report methods
  async updateFullReport(id: number, fullReport: any): Promise<void> {
    await this.db
      .update(analyses)
      .set({
        fullReport,
        updatedAt: new Date()
      })
      .where(eq(analyses.id, id));
  }

  async updateReportUrl(id: number, reportUrl: string): Promise<void> {
    await this.db
      .update(analyses)
      .set({
        reportUrl,
        updatedAt: new Date()
      })
      .where(eq(analyses.id, id));
  }

  async updateReportObjectKey(id: number, objectKey: string): Promise<void> {
    await this.db
      .update(analyses)
      .set({
        reportObjectKey: objectKey,
        updatedAt: new Date()
      })
      .where(eq(analyses.id, id));
  }

  async updateUploadedFileKey(id: number, fileKey: string): Promise<void> {
    await this.db
      .update(analyses)
      .set({
        uploadedFileKey: fileKey,
        updatedAt: new Date()
      })
      .where(eq(analyses.id, id));
  }

  async updatePaymentStatus(
    id: number, 
    status: 'pending' | 'completed', 
    userId?: number
  ): Promise<void> {
    const updateData: any = {
      paymentStatus: status,
      updatedAt: new Date()
    };
    
    if (userId) {
      updateData.userId = userId;
    }
    
    await this.db
      .update(analyses)
      .set(updateData)
      .where(eq(analyses.id, id));
  }

  // Cleanup method for graceful shutdown
  async close(): Promise<void> {
    await this.client.end();
  }
}

// Create storage instance
let storage: IStorage;

if (process.env.DATABASE_URL) {
  console.log('📊 Using PostgreSQL database storage');
  storage = new DatabaseStorage();
} else {
  console.log('⚠️  Using in-memory storage (data will be lost on restart)');
  const { MemStorage } = require('./storage');
  storage = new MemStorage();
}

export { storage };
