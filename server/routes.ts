import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./db-storage";
import multer from "multer";
import * as analyzer from "./analyzer";
import reportRoutes from "./routes/report";
import { objectStorage } from "./object-storage";
import path from "path";

// Configure multer for memory storage (we'll upload to object storage)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (_req, file, cb) => {
    // Only accept zip and tar files
    const ext = path.extname(file.originalname).toLowerCase();
    if (ext === '.zip' || ext === '.tar' || ext === '.gz') {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP and TAR files are allowed'));
    }
  }
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Register premium report routes
  app.use('/api/report', reportRoutes);
  // Helper function to get file size error message
  const getFileSizeErrorMessage = () => {
    const tutorialUrl = process.env.TUTORIAL_URL || "https://docs.example.com/how-to-clean-zip-files";

    return `Your file is larger than 50MB. This usually happens when unnecessary folders are included.

Common folders to remove:
• node_modules (Node.js dependencies)
• .git (Git version control)
• dist or build (Build outputs)
• vendor (PHP/Composer dependencies)

Need help cleaning your files? Visit: ${tutorialUrl}`;
  };

  // File upload analysis endpoint with error handling
  app.post('/api/analyze/upload', (req: Request, res: Response, next: Function) => {
    upload.single('file')(req, res, (err: any) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            message: getFileSizeErrorMessage()
          });
        }
        return res.status(400).json({
          message: err.message || 'File upload error'
        });
      }
      next();
    });
  }, async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const { description, projectType } = req.body;

      // Create a new analysis
      const analysisId = await storage.createAnalysis({
        type: 'file',
        description: description || '',
        projectType: projectType || 'webapp',
        status: 'processing'
      });

      // Upload file to object storage
      let uploadedFileKey: string | null = null;
      try {
        const fileExtension = path.extname(req.file.originalname).toLowerCase().substring(1);
        uploadedFileKey = await objectStorage.uploadFile(req.file.buffer, analysisId, fileExtension);
        await storage.updateUploadedFileKey(analysisId, uploadedFileKey);
        console.log(`File uploaded to object storage: ${uploadedFileKey}`);
      } catch (uploadError) {
        console.error('Failed to upload file to object storage:', uploadError);
        // Continue with local processing as fallback
      }

      // Start the analysis process using the uploaded file buffer
      const result = await analyzer.analyzeUploadedFileFromBuffer(req.file.buffer, req.file.originalname, {
        analysisId,
        description,
        projectType
      });
      
      // Update analysis with results
      await storage.updateAnalysisResults(analysisId, result);

      // Update status to completed
      await storage.updateAnalysisStatus(analysisId, 'completed');

      // Return result with analysisId
      return res.status(200).json({
        ...result,
        analysisId
      });
    } catch (error) {
      console.error("Upload analysis error:", error);
      return res.status(500).json({ 
        message: error instanceof Error ? error.message : 'An error occurred during analysis' 
      });
    }
  });

  // GitHub repo analysis endpoint
  app.post('/api/analyze/github', async (req: Request, res: Response) => {
    try {
      const { repoUrl, description, projectType } = req.body;
      
      if (!repoUrl) {
        return res.status(400).json({ message: 'Repository URL is required' });
      }
      
      // Validate GitHub URL format
      const githubUrlRegex = /^https:\/\/github\.com\/[^\/]+\/[^\/]+$/;
      if (!githubUrlRegex.test(repoUrl)) {
        return res.status(400).json({ message: 'Invalid GitHub repository URL' });
      }
      
      // Create a new analysis
      const analysisId = await storage.createAnalysis({
        type: 'github',
        repoUrl,
        description: description || '',
        projectType: projectType || 'webapp',
        status: 'processing'
      });
      
      // Start the analysis process
      const result = await analyzer.analyzeGithubRepo(repoUrl, {
        analysisId,
        description,
        projectType
      });
      
      // Update analysis with results
      await storage.updateAnalysisResults(analysisId, result);

      // Update status to completed
      await storage.updateAnalysisStatus(analysisId, 'completed');

      // Return result with analysisId
      return res.status(200).json({
        ...result,
        analysisId
      });
    } catch (error) {
      console.error("GitHub analysis error:", error);
      return res.status(500).json({ 
        message: error instanceof Error ? error.message : 'An error occurred during analysis' 
      });
    }
  });

  // Get analysis report endpoint
  app.get('/api/report/:analysisId', async (req: Request, res: Response) => {
    try {
      const analysisId = parseInt(req.params.analysisId);
      
      if (isNaN(analysisId)) {
        return res.status(400).json({ message: 'Invalid analysis ID' });
      }
      
      const analysis = await storage.getAnalysis(analysisId);
      
      if (!analysis) {
        return res.status(404).json({ message: 'Analysis not found' });
      }
      
      if (analysis.status === 'processing') {
        return res.status(202).json({ 
          message: 'Analysis is still processing',
          progress: analysis.progress
        });
      }
      
      return res.status(200).json({
        projectInfo: analysis.projectInfo,
        scoreData: analysis.scoreData
      });
    } catch (error) {
      console.error("Get report error:", error);
      return res.status(500).json({ 
        message: error instanceof Error ? error.message : 'An error occurred retrieving the report' 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
