import Stripe from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_...', {
  apiVersion: '2025-07-30.basil',
});

export interface CheckoutSessionData {
  analysisId: number;
  projectName: string;
  customerEmail?: string;
}

/**
 * Create a Stripe checkout session for report payment
 */
export async function createCheckoutSession(data: CheckoutSessionData): Promise<string> {
  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `MVP Readiness Report - ${data.projectName}`,
              description: 'Comprehensive analysis with actionable insights, deployment checklist, and personalized roadmap',
              images: [], // Add your product image URLs here
            },
            unit_amount: 999, // $9.99 in cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.FRONTEND_URL || 'http://localhost:5000'}/report/processing/${data.analysisId}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL || 'http://localhost:5000'}/report/payment/${data.analysisId}?cancelled=true`,
      metadata: {
        analysisId: data.analysisId.toString(),
        projectName: data.projectName,
      },
      customer_email: data.customerEmail,
      allow_promotion_codes: true, // Allow discount codes
    });

    return session.url!;
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    throw new Error('Failed to create checkout session');
  }
}

/**
 * Verify a checkout session was completed successfully
 */
export async function verifyCheckoutSession(sessionId: string): Promise<{
  success: boolean;
  analysisId?: number;
  projectName?: string;
  paymentIntentId?: string;
}> {
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (session.payment_status === 'paid') {
      return {
        success: true,
        analysisId: parseInt(session.metadata?.analysisId || '0'),
        projectName: session.metadata?.projectName,
        paymentIntentId: session.payment_intent as string,
      };
    }
    
    return { success: false };
  } catch (error) {
    console.error('Error verifying checkout session:', error);
    return { success: false };
  }
}

/**
 * Handle Stripe webhook events
 */
export async function handleWebhookEvent(
  body: string | Buffer,
  signature: string
): Promise<{
  success: boolean;
  analysisId?: number;
  eventType?: string;
}> {
  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      throw new Error('Stripe webhook secret not configured');
    }

    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    
    console.log('Received Stripe webhook:', event.type);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        if (session.payment_status === 'paid') {
          const analysisId = parseInt(session.metadata?.analysisId || '0');
          
          if (analysisId) {
            return {
              success: true,
              analysisId,
              eventType: event.type,
            };
          }
        }
        break;
      }
      
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('Payment succeeded:', paymentIntent.id);
        break;
      }
      
      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('Payment failed:', paymentIntent.id);
        break;
      }
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling Stripe webhook:', error);
    return { success: false };
  }
}

/**
 * Create a coupon for discounts
 */
export async function createCoupon(
  code: string,
  percentOff: number,
  duration: 'once' | 'repeating' | 'forever' = 'once'
): Promise<Stripe.Coupon> {
  try {
    return await stripe.coupons.create({
      id: code.toUpperCase(),
      percent_off: percentOff,
      duration,
      max_redemptions: duration === 'once' ? 100 : undefined,
    });
  } catch (error) {
    console.error('Error creating coupon:', error);
    throw error;
  }
}

export { stripe };
