import { analyses, type Analysis, type InsertAnalysis } from "@shared/schema";

export interface IStorage {
  createAnalysis(analysis: InsertAnalysis): Promise<number>;
  getAnalysis(id: number): Promise<Analysis | undefined>;
  updateAnalysisStatus(id: number, status: 'processing' | 'completed' | 'failed', progress?: any): Promise<void>;
  updateAnalysisResults(id: number, results: any): Promise<void>;
  listAnalyses(): Promise<Analysis[]>;
  
  // Premium report methods
  updateFullReport(id: number, fullReport: any): Promise<void>;
  updateReportUrl(id: number, reportUrl: string): Promise<void>;
  updateReportObjectKey(id: number, objectKey: string): Promise<void>;
  updateUploadedFileKey(id: number, fileKey: string): Promise<void>;
  updatePaymentStatus(id: number, status: 'pending' | 'completed', userId?: number): Promise<void>;
}

export class MemStorage implements IStorage {
  private analyses: Map<number, Analysis>;
  private currentId: number;

  constructor() {
    this.analyses = new Map();
    this.currentId = 1;
  }

  async createAnalysis(analysis: InsertAnalysis): Promise<number> {
    const id = this.currentId++;
    
    const newAnalysis: Analysis = {
      id,
      type: analysis.type,
      repoUrl: analysis.repoUrl || null,
      description: analysis.description || null,
      projectType: analysis.projectType,
      status: analysis.status,
      progress: {
        stage: "idle",
        currentStep: "",
        percentage: 0,
        steps: [
          { name: "Extracting Files", status: "pending" },
          { name: "Parsing Files", status: "pending" },
          { name: "Analyzing Code", status: "pending" },
          { name: "Generating Report", status: "pending" }
        ]
      },
      projectInfo: null,
      scoreData: null,
      fullReport: null,
      reportUrl: null,
      reportObjectKey: null,
      uploadedFileKey: null,
      paymentStatus: 'pending',
      userId: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.analyses.set(id, newAnalysis);
    return id;
  }

  async getAnalysis(id: number): Promise<Analysis | undefined> {
    return this.analyses.get(id);
  }

  async updateAnalysisStatus(id: number, status: 'processing' | 'completed' | 'failed', progress?: any): Promise<void> {
    const analysis = this.analyses.get(id);
    
    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }
    
    analysis.status = status;
    analysis.updatedAt = new Date();
    
    if (progress) {
      analysis.progress = progress;
    }
    
    this.analyses.set(id, analysis);
  }

  async updateAnalysisResults(id: number, results: any): Promise<void> {
    const analysis = this.analyses.get(id);
    
    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }
    
    analysis.status = 'completed';
    analysis.updatedAt = new Date();
    analysis.progress = results.progress;
    analysis.projectInfo = results.projectInfo;
    analysis.scoreData = results.scoreData;
    
    this.analyses.set(id, analysis);
  }

  async listAnalyses(): Promise<Analysis[]> {
    return Array.from(this.analyses.values());
  }
  
  // Premium report methods implementation
  async updateFullReport(id: number, fullReport: any): Promise<void> {
    const analysis = this.analyses.get(id);
    
    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }
    
    analysis.fullReport = fullReport;
    analysis.updatedAt = new Date();
    
    this.analyses.set(id, analysis);
  }
  
  async updateReportUrl(id: number, reportUrl: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.reportUrl = reportUrl;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updateReportObjectKey(id: number, objectKey: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.reportObjectKey = objectKey;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updateUploadedFileKey(id: number, fileKey: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.uploadedFileKey = fileKey;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }
  
  async updatePaymentStatus(id: number, status: 'pending' | 'completed', userId?: number): Promise<void> {
    const analysis = this.analyses.get(id);
    
    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }
    
    analysis.paymentStatus = status;
    if (userId) {
      analysis.userId = userId;
    }
    analysis.updatedAt = new Date();
    
    this.analyses.set(id, analysis);
  }
}

export const storage = new MemStorage();
