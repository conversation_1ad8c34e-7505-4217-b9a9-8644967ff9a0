import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

// Set title for the application
document.title = "MVP ScoreGen Widget";

// Add meta tags
const meta = document.createElement('meta');
meta.name = 'description';
meta.content = 'Analyze your project\'s MVP readiness with AI-powered scoring';
document.head.appendChild(meta);

// Add font from Google Fonts
const fontLink = document.createElement('link');
fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap';
fontLink.rel = 'stylesheet';
document.head.appendChild(fontLink);

createRoot(document.getElementById("root")!).render(<App />);
