import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import UploadPanel from "@/components/UploadPanel";
import ProjectInfoPanel from "@/components/ProjectInfoPanel";
import AnalysisProgress from "@/components/AnalysisProgress";
import ResultsPanel from "@/components/ResultsPanel";
import AuthRequiredModal from "@/components/AuthRequiredModal";
import { Card } from "@/components/ui/card";

export type AnalysisStage = "idle" | "uploading" | "analyzing" | "completed" | "auth_required";

export interface ProjectInfo {
  name: string;
  type: string;
  fileCount: number;
  technologies: string[];
}

export interface ProgressInfo {
  stage: AnalysisStage;
  currentStep: string;
  percentage: number;
  steps: {
    name: string;
    status: "completed" | "current" | "pending";
    duration?: string;
  }[];
}

export interface ScoreData {
  total: number;
  percentage: number;
  color: string;
  category: string;
  strengths: string[];
  weaknesses: string[];
  criterionScores: {
    name: string;
    description: string;
    score: number;
    feedback: string;
  }[];
  recommendations: {
    id: number;
    title: string;
    description: string;
    priority: "High" | "Medium" | "Low";
  }[];
  radarData: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string;
      borderColor: string;
      pointBackgroundColor: string;
      pointBorderColor: string;
      pointHoverBackgroundColor: string;
      pointHoverBorderColor: string;
      borderDash?: number[];
    }[];
  };
}

export default function Home() {
  const [stage, setStage] = useState<AnalysisStage>("idle");
  const [progress, setProgress] = useState<ProgressInfo>({
    stage: "idle",
    currentStep: "",
    percentage: 0,
    steps: [
      { name: "Extracting Files", status: "pending" },
      { name: "Parsing Files", status: "pending" },
      { name: "Analyzing Code", status: "pending" },
      { name: "Generating Report", status: "pending" }
    ]
  });
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);
  const [scoreData, setScoreData] = useState<ScoreData | null>(null);
  const [analysisId, setAnalysisId] = useState<number | null>(null);

  // Check if user is authenticated
  const { data: user } = useQuery({
    queryKey: ['/auth/me'],
    retry: false
  });

  const handleAnalysisStart = () => {
    setStage("analyzing");
    setProgress({
      stage: "analyzing",
      currentStep: "Extracting Files",
      percentage: 10,
      steps: [
        { name: "Extracting Files", status: "current" },
        { name: "Parsing Files", status: "pending" },
        { name: "Analyzing Code", status: "pending" },
        { name: "Generating Report", status: "pending" }
      ]
    });
  };

  const handleAnalysisComplete = (projectInfo: ProjectInfo, scoreData: ScoreData, analysisId: number) => {
    setProjectInfo(projectInfo);
    setScoreData(scoreData);
    setAnalysisId(analysisId);

    // Check if user is authenticated
    if (user?.user) {
      // User is authenticated, show results immediately
      setStage("completed");
    } else {
      // User needs to authenticate first
      setStage("auth_required");
    }
  };

  const handleAuthSuccess = () => {
    // After successful authentication, show the results
    setStage("completed");
  };

  return (
    <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-neutral-900">VibeComplete</h1>
        <p className="text-neutral-600 mt-2">Analyze your project's MVP readiness with AI-powered scoring</p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Left Panel - Upload/Input Section */}
        <div className="lg:col-span-5">
          <UploadPanel
            onAnalysisStart={handleAnalysisStart}
            onAnalysisComplete={handleAnalysisComplete}
            setProgress={setProgress}
          />

          {projectInfo && stage === "completed" && (
            <ProjectInfoPanel projectInfo={projectInfo} />
          )}
        </div>

        {/* Right Panel - Results Section */}
        <div className="lg:col-span-7">
          {stage === "analyzing" && (
            <AnalysisProgress progress={progress} />
          )}
          
          {stage === "completed" && scoreData && (
            <ResultsPanel scoreData={scoreData} analysisId={analysisId} />
          )}
          
          {stage === "idle" && (
            <Card className="p-6 bg-white rounded-xl shadow-md">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-primary-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h2 className="text-xl font-semibold mb-2">Ready to Analyze Your Project</h2>
                <p className="text-neutral-600 max-w-md">
                  Upload a ZIP/TAR file or enter a GitHub repository URL to evaluate your project's MVP readiness.
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Auth Required Modal */}
      <AuthRequiredModal
        isOpen={stage === "auth_required"}
        onAuthSuccess={handleAuthSuccess}
      />
    </div>
  );
}
