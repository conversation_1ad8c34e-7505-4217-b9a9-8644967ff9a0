import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import Home from "@/pages/Home";
import ReportPreview from "@/pages/ReportPreview";
import ReportPayment from "@/pages/ReportPayment";
import ReportProcessing from "@/pages/ReportProcessing";
import ReportDownload from "@/pages/ReportDownload";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/report/preview/:id" component={ReportPreview} />
      <Route path="/report/payment/:id" component={ReportPayment} />
      <Route path="/report/processing/:id" component={ReportProcessing} />
      <Route path="/report/download/:id" component={ReportDownload} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router />
      <Toaster />
    </QueryClientProvider>
  );
}

export default App;
