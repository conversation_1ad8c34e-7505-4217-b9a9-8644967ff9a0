import { useQuery } from '@tanstack/react-query';
import { ReactNode } from 'react';
import LoginPage from '@/pages/Login';

interface AuthGuardProps {
  children: ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { data: user, isLoading } = useQuery({
    queryKey: ['/auth/me'],
    retry: false
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!user?.user) {
    return <LoginPage />;
  }

  return <>{children}</>;
}
