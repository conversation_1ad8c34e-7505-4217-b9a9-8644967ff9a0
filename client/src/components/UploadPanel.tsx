import { useState, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { uploadProject, analyzeGithubRepo } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { AnalysisStage, ProgressInfo, ProjectInfo, ScoreData } from "@/pages/Home";

interface UploadPanelProps {
  onAnalysisStart: () => void;
  setProjectInfo: (info: ProjectInfo) => void;
  setProgress: (progress: ProgressInfo) => void;
  setScoreData: (data: ScoreData) => void;
  setStage: (stage: AnalysisStage) => void;
  setAnalysisId: (id: number) => void;
}

const githubRepoUrlSchema = z.string().trim().url().startsWith('https://github.com/');

export default function UploadPanel({
  onAnalysisStart,
  setProjectInfo,
  setProgress,
  setScoreData,
  setStage,
  setAnalysisId
}: UploadPanelProps) {
  const [activeTab, setActiveTab] = useState<'file' | 'repo'>('file');
  const [repoUrl, setRepoUrl] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [projectType, setProjectType] = useState<string>('webapp');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const projectTypes = [
    { id: 'webapp', label: 'Web App' },
    { id: 'mobile', label: 'Mobile App' },
    { id: 'api', label: 'API/Backend' },
    { id: 'other', label: 'Other' }
  ];

  const getFileSizeErrorMessage = () => {
    const tutorialUrl = process.env.TUTORIAL_URL || "https://docs.example.com/how-to-clean-zip-files";

    return `Your file is larger than 50MB. This usually happens when unnecessary folders are included.

Common folders to remove:
• node_modules (Node.js dependencies)
• .git (Git version control)
• dist or build (Build outputs)
• vendor (PHP/Composer dependencies)

Need help cleaning your files? Visit: ${tutorialUrl}`;
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];

      // Check file size (50MB limit)
      if (selectedFile.size > 50 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: getFileSizeErrorMessage(),
          variant: "destructive"
        });
        return;
      }
      
      // Check file type
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !['zip', 'tar', 'gz'].includes(fileExtension)) {
        toast({
          title: "Invalid file format",
          description: "Please upload a ZIP or TAR file",
          variant: "destructive"
        });
        return;
      }
      
      setFile(selectedFile);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      
      // Check file size (50MB limit)
      if (droppedFile.size > 50 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: getFileSizeErrorMessage(),
          variant: "destructive"
        });
        return;
      }
      
      // Check file type
      const fileExtension = droppedFile.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !['zip', 'tar', 'gz'].includes(fileExtension)) {
        toast({
          title: "Invalid file format",
          description: "Please upload a ZIP or TAR file",
          variant: "destructive"
        });
        return;
      }
      
      setFile(droppedFile);
    }
  };

  const validateGithubUrl = (url: string): boolean => {
    try {
      githubRepoUrlSchema.parse(url);
      return true;
    } catch (error) {
      return false;
    }
  };

  const analyzeProject = async () => {
    try {
      setIsAnalyzing(true);
      onAnalysisStart();
      
      let analysisResult;
      
      if (activeTab === 'file' && file) {
        // Update progress for file upload
        setProgress({
          stage: "analyzing",
          currentStep: "Uploading file",
          percentage: 5,
          steps: [
            { name: "Extracting Files", status: "pending" },
            { name: "Parsing Files", status: "pending" },
            { name: "Analyzing Code", status: "pending" },
            { name: "Generating Report", status: "pending" }
          ]
        });
        
        analysisResult = await uploadProject(file, projectDescription, projectType);
      } else if (activeTab === 'repo' && repoUrl) {
        // Update progress for repo cloning
        setProgress({
          stage: "analyzing",
          currentStep: "Cloning repository",
          percentage: 5,
          steps: [
            { name: "Fetching Repository", status: "pending" },
            { name: "Parsing Files", status: "pending" },
            { name: "Analyzing Code", status: "pending" },
            { name: "Generating Report", status: "pending" }
          ]
        });
        
        analysisResult = await analyzeGithubRepo(repoUrl, projectDescription, projectType);
      } else {
        throw new Error(activeTab === 'file' 
          ? "Please upload a project file" 
          : "Please enter a valid GitHub repository URL");
      }
      
      // Update the progress and results based on the response
      updateProgress(analysisResult.progress);
      
      // Short timeout to show the final progress state before showing results
      setTimeout(() => {
        setProjectInfo(analysisResult.projectInfo);
        setScoreData(analysisResult.scoreData);
        setAnalysisId(analysisResult.analysisId);
        setStage("completed");
        setIsAnalyzing(false);
      }, 1000);
      
    } catch (error) {
      setStage("idle");
      setIsAnalyzing(false);
      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const updateProgress = (progress: ProgressInfo) => {
    setProgress(progress);
  };

  return (
    <Card className="bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold text-neutral-900 mb-4">VibeComplete Analysis</h2>
        
        {/* Tabs */}
        <div className="flex border-b border-neutral-200 mb-6">
          <button 
            className={`px-4 py-2 font-medium ${activeTab === 'file' 
              ? 'text-blue-600 border-b-2 border-blue-500' 
              : 'text-neutral-500 hover:text-neutral-700'}`}
            onClick={() => setActiveTab('file')}
          >
            File Upload
          </button>
          <button 
            className={`px-4 py-2 font-medium ${activeTab === 'repo' 
              ? 'text-blue-600 border-b-2 border-blue-500' 
              : 'text-neutral-500 hover:text-neutral-700'}`}
            onClick={() => setActiveTab('repo')}
          >
            GitHub Repository
          </button>
        </div>
        
        {/* File Upload Tab */}
        {activeTab === 'file' && (
          <div className="mb-6">
            <div 
              className={`border-2 border-dashed rounded-lg p-6 text-center hover:bg-neutral-50 transition-colors cursor-pointer ${file ? 'border-blue-400 bg-blue-50' : 'border-neutral-300 bg-neutral-50'}`}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={handleBrowseClick}
            >
              <div className="flex flex-col items-center justify-center space-y-2">
                {file ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-blue-600 font-medium">{file.name}</p>
                    <p className="text-neutral-500 text-sm">{(file.size / (1024 * 1024)).toFixed(2)} MB</p>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-neutral-600 font-medium">Drag and drop your ZIP/TAR file here</p>
                    <p className="text-neutral-400 text-sm">or</p>
                    <Button>Browse Files</Button>
                    <p className="text-neutral-400 text-xs mt-2">Maximum file size: 50MB</p>
                  </>
                )}
              </div>
              <input 
                type="file" 
                className="hidden" 
                accept=".zip,.tar,.tar.gz" 
                ref={fileInputRef}
                onChange={handleFileUpload}
              />
            </div>
            
            <div className="mt-4 text-sm text-neutral-500">
              <p>Supported formats: ZIP, TAR (max 50MB)</p>
            </div>
          </div>
        )}
        
        {/* Repository Tab */}
        {activeTab === 'repo' && (
          <div className="mb-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="repoUrl">GitHub Repository URL</Label>
                <div className="flex mt-1">
                  <Input
                    type="text"
                    id="repoUrl"
                    placeholder="https://github.com/username/repo"
                    value={repoUrl}
                    onChange={(e) => setRepoUrl(e.target.value)}
                    className="rounded-r-none"
                  />
                  <Button 
                    className="rounded-l-none"
                    onClick={() => {
                      if (!validateGithubUrl(repoUrl)) {
                        toast({
                          title: "Invalid GitHub URL",
                          description: "Please enter a valid GitHub repository URL",
                          variant: "destructive"
                        });
                        return;
                      }
                      analyzeProject();
                    }}
                    disabled={isAnalyzing || !repoUrl}
                  >
                    Run VibeComplete
                  </Button>
                </div>
                <p className="mt-1 text-xs text-neutral-500">Example: https://github.com/facebook/react</p>
              </div>
              
              <div className="p-4 bg-neutral-50 rounded-md border border-neutral-200">
                <h3 className="font-medium text-neutral-700 mb-2">Repository Requirements:</h3>
                <ul className="text-sm text-neutral-600 space-y-1 list-disc list-inside">
                  <li>Must be a public repository</li>
                  <li>Project size should be under 100MB</li>
                  <li>Standard project structure recommended</li>
                </ul>
              </div>
            </div>
          </div>
        )}
        
        {/* Optional Project Description */}
        <div className="mb-6">
          <Label htmlFor="projectDescription">Project Description (Optional)</Label>
          <Textarea
            id="projectDescription"
            placeholder="Describe your project goals to improve AI analysis accuracy..."
            className="mt-1"
            rows={3}
            value={projectDescription}
            onChange={(e) => setProjectDescription(e.target.value)}
          />
          <p className="mt-1 text-xs text-neutral-500">Providing context helps our AI better understand your project's intent and scope.</p>
        </div>
        
        {/* Project Type */}
        <div className="mb-6">
          <Label className="block mb-2">Project Type</Label>
          <RadioGroup value={projectType} onValueChange={setProjectType} className="grid grid-cols-2 gap-3">
            {projectTypes.map((type) => (
              <div className="flex items-center" key={type.id}>
                <RadioGroupItem id={`type-${type.id}`} value={type.id} />
                <Label htmlFor={`type-${type.id}`} className="ml-2">
                  {type.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        
        {/* Submit Button (only shown for file upload tab) */}
        {activeTab === 'file' && (
          <Button 
            className="w-full py-6" 
            onClick={analyzeProject}
            disabled={isAnalyzing || !file}
          >
            {isAnalyzing ? 'Analyzing...' : 'Start VibeComplete Analysis'}
          </Button>
        )}
      </div>
    </Card>
  );
}
